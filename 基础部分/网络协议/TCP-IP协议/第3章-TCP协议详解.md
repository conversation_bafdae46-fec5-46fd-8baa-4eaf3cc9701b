# 第3章 TCP协议详解

## 学习目标
- 深入理解TCP协议的工作原理
- 掌握TCP数据段的结构和字段含义
- 理解TCP连接的建立、维护和释放过程
- 掌握TCP的流量控制和拥塞控制机制
- 了解TCP的可靠性保证机制

## 3.1 TCP协议概述

### 3.1.1 TCP协议的特点

传输控制协议（TCP）是一个面向连接的、可靠的传输层协议，具有以下特点：

1. **面向连接**：通信前需要建立连接
2. **可靠传输**：保证数据的正确性和完整性
3. **流量控制**：防止发送方发送过快
4. **拥塞控制**：避免网络拥塞
5. **全双工通信**：支持双向数据传输
6. **字节流服务**：将数据视为连续的字节流

### 3.1.2 TCP与UDP的比较

| 特征 | TCP | UDP |
|------|-----|-----|
| 连接性 | 面向连接 | 无连接 |
| 可靠性 | 可靠 | 不可靠 |
| 速度 | 较慢 | 较快 |
| 开销 | 大 | 小 |
| 应用场景 | 文件传输、网页浏览 | 视频流、DNS查询 |

## 3.2 TCP数据段结构

### 3.2.1 TCP头部格式

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|          Source Port          |       Destination Port       |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                        Sequence Number                        |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                    Acknowledgment Number                      |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|  Data |           |U|A|P|R|S|F|                               |
| Offset| Reserved  |R|C|S|S|Y|I|            Window             |
|       |           |G|K|H|T|N|N|                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|           Checksum            |         Urgent Pointer        |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                    Options                    |    Padding    |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                             data                              |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

### 3.2.2 字段详解

#### 源端口（Source Port）- 16位
- 发送方的端口号
- 范围：0-65535

#### 目的端口（Destination Port）- 16位
- 接收方的端口号
- 用于标识目标应用程序

#### 序列号（Sequence Number）- 32位
- 标识数据段中第一个字节的序号
- 用于数据排序和重复检测

#### 确认号（Acknowledgment Number）- 32位
- 期望接收的下一个字节的序号
- 只有ACK标志位为1时才有效

#### 数据偏移（Data Offset）- 4位
- TCP头部长度，单位为32位字
- 最小值为5（20字节），最大值为15（60字节）

#### 保留位（Reserved）- 6位
- 保留字段，必须为0

#### 控制位（Flags）- 6位
- **URG**：紧急指针有效
- **ACK**：确认号有效
- **PSH**：推送数据
- **RST**：重置连接
- **SYN**：同步序列号
- **FIN**：结束连接

#### 窗口大小（Window）- 16位
- 接收窗口大小
- 用于流量控制

#### 校验和（Checksum）- 16位
- 用于检测传输错误
- 包括头部、数据和伪头部

#### 紧急指针（Urgent Pointer）- 16位
- 指向紧急数据的最后一个字节
- 只有URG标志位为1时才有效

#### 选项（Options）- 可变长度
- 扩展TCP功能
- 如最大段大小（MSS）、窗口扩大等

## 3.3 TCP连接管理

### 3.3.1 三次握手（连接建立）

TCP连接建立需要三次握手过程：

```
客户端                    服务器
   |                        |
   |-------- SYN ---------->|  (1) SYN=1, seq=x
   |                        |
   |<----- SYN+ACK ---------|  (2) SYN=1, ACK=1, seq=y, ack=x+1
   |                        |
   |-------- ACK ---------->|  (3) ACK=1, seq=x+1, ack=y+1
   |                        |
   |     连接建立完成        |
```

#### 详细过程

1. **第一次握手**：
   - 客户端发送SYN段，SYN=1，seq=x
   - 客户端进入SYN_SENT状态

2. **第二次握手**：
   - 服务器发送SYN+ACK段，SYN=1，ACK=1，seq=y，ack=x+1
   - 服务器进入SYN_RCVD状态

3. **第三次握手**：
   - 客户端发送ACK段，ACK=1，seq=x+1，ack=y+1
   - 双方进入ESTABLISHED状态

#### 为什么需要三次握手？

1. **防止旧连接**：避免旧的重复连接请求被接受
2. **同步序列号**：双方都需要确认对方的初始序列号
3. **资源分配**：确保双方都准备好进行通信

### 3.3.2 四次挥手（连接释放）

TCP连接释放需要四次挥手过程：

```
客户端                    服务器
   |                        |
   |-------- FIN ---------->|  (1) FIN=1, seq=u
   |                        |
   |<------- ACK -----------|  (2) ACK=1, ack=u+1
   |                        |
   |<------- FIN -----------|  (3) FIN=1, seq=v
   |                        |
   |-------- ACK ---------->|  (4) ACK=1, ack=v+1
   |                        |
   |     连接释放完成        |
```

#### 详细过程

1. **第一次挥手**：
   - 客户端发送FIN段，FIN=1，seq=u
   - 客户端进入FIN_WAIT_1状态

2. **第二次挥手**：
   - 服务器发送ACK段，ACK=1，ack=u+1
   - 服务器进入CLOSE_WAIT状态，客户端进入FIN_WAIT_2状态

3. **第三次挥手**：
   - 服务器发送FIN段，FIN=1，seq=v
   - 服务器进入LAST_ACK状态

4. **第四次挥手**：
   - 客户端发送ACK段，ACK=1，ack=v+1
   - 客户端进入TIME_WAIT状态，服务器进入CLOSED状态

#### TIME_WAIT状态

- **持续时间**：2MSL（Maximum Segment Lifetime）
- **目的**：
  1. 确保最后的ACK能够到达服务器
  2. 防止旧连接的数据段影响新连接

## 3.4 TCP可靠性机制

### 3.4.1 序列号和确认号

- **序列号**：标识数据段的位置
- **确认号**：表示期望接收的下一个字节
- **累积确认**：确认号之前的所有数据都已正确接收

### 3.4.2 重传机制

#### 超时重传
- **RTO**（Retransmission Timeout）：重传超时时间
- **算法**：基于RTT（Round Trip Time）动态计算
- **指数退避**：每次重传时RTO翻倍

#### 快速重传
- **触发条件**：收到3个重复ACK
- **优势**：不等待超时，快速恢复
- **配合**：通常与快速恢复算法一起使用

### 3.4.3 校验和

TCP校验和包括：
1. **伪头部**：源IP、目的IP、协议号、TCP长度
2. **TCP头部**：所有TCP头部字段
3. **数据部分**：TCP载荷数据

## 3.5 TCP流量控制

### 3.5.1 滑动窗口机制

流量控制通过滑动窗口机制实现：

- **发送窗口**：发送方可以发送的数据量
- **接收窗口**：接收方可以接收的数据量
- **窗口大小**：由接收方在TCP头部中通告

### 3.5.2 窗口管理

#### 发送窗口状态
1. **已发送已确认**：可以发送新数据
2. **已发送未确认**：等待确认
3. **未发送可发送**：窗口内可发送
4. **未发送不可发送**：超出窗口

#### 零窗口问题
- **现象**：接收方窗口为0，发送方停止发送
- **解决**：窗口探测机制，定期发送1字节数据

## 3.6 TCP拥塞控制

### 3.6.1 拥塞控制算法

#### 慢启动（Slow Start）
- **初始值**：拥塞窗口cwnd = 1 MSS
- **增长**：每收到一个ACK，cwnd增加1 MSS
- **特点**：指数增长

#### 拥塞避免（Congestion Avoidance）
- **触发**：cwnd达到慢启动阈值ssthresh
- **增长**：每个RTT，cwnd增加1 MSS
- **特点**：线性增长

#### 快速重传和快速恢复
- **快速重传**：收到3个重复ACK立即重传
- **快速恢复**：不进入慢启动，直接进入拥塞避免

### 3.6.2 拥塞控制状态转换

```
慢启动 -----> 拥塞避免 -----> 快速恢复
   ^             |              |
   |             v              |
   +<----- 超时重传 <-----------+
```

## 3.7 TCP性能优化

### 3.7.1 Nagle算法

- **目的**：减少小数据包的发送
- **原理**：等待足够数据或确认后再发送
- **问题**：可能增加延迟

### 3.7.2 延迟确认

- **目的**：减少ACK数据包数量
- **原理**：延迟一段时间再发送ACK
- **限制**：最多延迟500ms或2个数据段

### 3.7.3 窗口扩大

- **问题**：16位窗口字段限制最大窗口为64KB
- **解决**：使用窗口扩大选项，支持更大窗口
- **计算**：实际窗口 = 窗口字段 × 2^扩大因子

## 本章小结

TCP协议通过三次握手建立连接，四次挥手释放连接，使用序列号、确认号、重传机制保证可靠性，通过滑动窗口实现流量控制，通过拥塞控制算法避免网络拥塞。理解TCP协议的工作原理对于网络编程、性能优化和安全分析都非常重要。

## 实验练习

1. 使用Wireshark分析TCP三次握手和四次挥手过程
2. 编写程序测试TCP连接的建立和释放
3. 分析TCP重传和拥塞控制的行为
4. 测试不同TCP参数对性能的影响

## 思考题

1. 为什么TCP连接建立需要三次握手而不是两次？
2. TIME_WAIT状态的作用是什么？
3. TCP如何区分网络拥塞和数据包丢失？
4. 在什么情况下应该禁用Nagle算法？
