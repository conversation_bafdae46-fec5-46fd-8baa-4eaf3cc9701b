# 第2章 IP协议详解

## 学习目标
- 深入理解IP协议的工作原理
- 掌握IP数据包的结构和字段含义
- 理解IP地址的分类和子网划分
- 了解IPv4和IPv6的区别
- 掌握IP路由的基本概念

## 2.1 IP协议概述

### 2.1.1 IP协议的作用

Internet Protocol（IP）是TCP/IP协议族中的核心协议，工作在网络层，主要功能包括：

1. **寻址**：为网络中的每个设备分配唯一地址
2. **路由**：确定数据包从源到目的地的路径
3. **分片**：将大数据包分割成适合传输的小片段
4. **重组**：在目的地重新组装分片的数据包

### 2.1.2 IP协议的特点

- **无连接**：发送数据前不需要建立连接
- **不可靠**：不保证数据包的可靠传输
- **尽力而为**：尽最大努力传输数据包
- **无状态**：不维护连接状态信息

## 2.2 IPv4数据包结构

### 2.2.1 IPv4头部格式

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|Version|  IHL  |Type of Service|          Total Length         |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|         Identification        |Flags|      Fragment Offset    |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|  Time to Live |    Protocol   |         Header Checksum       |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                       Source Address                          |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                    Destination Address                        |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                    Options                    |    Padding    |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

### 2.2.2 字段详解

#### 版本（Version）- 4位
- 指示IP协议的版本
- IPv4的值为4，IPv6的值为6

#### 头部长度（IHL）- 4位
- 指示IP头部的长度
- 单位为32位字（4字节）
- 最小值为5（20字节），最大值为15（60字节）

#### 服务类型（Type of Service）- 8位
- 指示数据包的服务质量要求
- 包含优先级、延迟、吞吐量、可靠性等信息

#### 总长度（Total Length）- 16位
- 指示整个IP数据包的长度（头部+数据）
- 单位为字节，最大值为65535字节

#### 标识（Identification）- 16位
- 用于标识分片的数据包
- 同一个原始数据包的所有分片具有相同的标识

#### 标志（Flags）- 3位
- **保留位（Reserved）**：必须为0
- **不分片位（DF）**：1表示不允许分片
- **更多分片位（MF）**：1表示后面还有分片

#### 分片偏移（Fragment Offset）- 13位
- 指示当前分片在原始数据包中的位置
- 单位为8字节

#### 生存时间（TTL）- 8位
- 数据包在网络中的最大跳数
- 每经过一个路由器减1，为0时丢弃

#### 协议（Protocol）- 8位
- 指示上层协议类型
- 常见值：1（ICMP）、6（TCP）、17（UDP）

#### 头部校验和（Header Checksum）- 16位
- 用于检测头部是否有错误
- 只校验头部，不校验数据部分

#### 源地址（Source Address）- 32位
- 发送方的IP地址

#### 目的地址（Destination Address）- 32位
- 接收方的IP地址

#### 选项（Options）- 可变长度
- 可选字段，用于扩展IP功能
- 如记录路由、时间戳等

## 2.3 IP地址

### 2.3.1 IPv4地址格式

IPv4地址由32位二进制数组成，通常用点分十进制表示：
- 格式：A.B.C.D
- 范围：0.0.0.0 到 ***************
- 示例：***********

### 2.3.2 IPv4地址分类

#### A类地址
- **范围**：******* 到 ***************
- **网络位**：8位
- **主机位**：24位
- **默认子网掩码**：********* (/8)
- **用途**：大型网络

#### B类地址
- **范围**：********* 到 ***************
- **网络位**：16位
- **主机位**：16位
- **默认子网掩码**：*********** (/16)
- **用途**：中型网络

#### C类地址
- **范围**：********* 到 ***************
- **网络位**：24位
- **主机位**：8位
- **默认子网掩码**：************* (/24)
- **用途**：小型网络

#### D类地址（组播）
- **范围**：********* 到 ***************
- **用途**：组播通信

#### E类地址（保留）
- **范围**：240.0.0.0 到 ***************
- **用途**：实验和保留

### 2.3.3 特殊IP地址

#### 私有地址
- **A类私有**：10.0.0.0/8
- **B类私有**：**********/12
- **C类私有**：***********/16

#### 特殊地址
- **环回地址**：*********/8
- **链路本地**：***********/16
- **广播地址**：***************
- **网络地址**：主机位全为0
- **广播地址**：主机位全为1

## 2.4 子网划分

### 2.4.1 子网掩码

子网掩码用于区分网络部分和主机部分：
- **作用**：确定IP地址的网络和主机部分
- **格式**：与IP地址相同的32位二进制数
- **表示方法**：点分十进制或CIDR记法

#### CIDR记法示例
- ***********/24 表示前24位为网络位
- 等价于子网掩码 *************

### 2.4.2 子网划分实例

将***********/24划分为4个子网：

1. **确定所需子网位数**：4个子网需要2位（2² = 4）
2. **新的子网掩码**：/26（24+2）
3. **子网列表**：
   - ***********/26（***********-************）
   - ************/26（************-***********26）
   - ***********28/26（***********29-***********90）
   - ***********92/26（***********93-*************）

## 2.5 IPv6简介

### 2.5.1 IPv6的特点

- **地址长度**：128位（IPv4为32位）
- **地址数量**：约3.4×10³⁸个地址
- **简化头部**：提高处理效率
- **内置安全**：IPSec是必选功能
- **自动配置**：支持无状态地址配置

### 2.5.2 IPv6地址格式

- **表示方法**：8组16进制数，用冒号分隔
- **示例**：2001:0db8:85a3:0000:0000:8a2e:0370:7334
- **简化规则**：
  - 前导零可省略
  - 连续的零可用::表示（只能用一次）
  - 简化后：2001:db8:85a3::8a2e:370:7334

## 2.6 IP路由基础

### 2.6.1 路由概念

路由是指数据包从源到目的地的路径选择过程：
- **路由表**：存储网络路径信息的表格
- **路由器**：执行路由功能的网络设备
- **路由协议**：用于交换路由信息的协议

### 2.6.2 路由表结构

典型的路由表包含：
- **目的网络**：目标网络地址
- **子网掩码**：网络掩码
- **下一跳**：下一个路由器地址
- **接口**：出口网络接口
- **度量值**：路径成本

### 2.6.3 路由选择过程

1. **查找路由表**：寻找匹配的目的网络
2. **最长匹配**：选择最具体的路由条目
3. **转发数据包**：发送到下一跳或直接交付
4. **TTL递减**：生存时间减1

## 本章小结

IP协议是网络层的核心协议，负责数据包的寻址和路由。IPv4使用32位地址，支持约43亿个地址，但面临地址耗尽问题。IPv6使用128位地址，提供了几乎无限的地址空间。理解IP协议的工作原理对于网络安全分析和故障排除至关重要。

## 实验练习

1. 使用Wireshark捕获并分析IP数据包
2. 计算给定网络的子网划分方案
3. 配置静态路由并测试连通性
4. 比较IPv4和IPv6数据包的结构差异

## 思考题

1. 为什么IP协议被设计为无连接和不可靠的？
2. 如何理解"最长匹配"路由选择原则？
3. IPv6相比IPv4有哪些主要改进？
4. 在什么情况下会发生IP分片？
