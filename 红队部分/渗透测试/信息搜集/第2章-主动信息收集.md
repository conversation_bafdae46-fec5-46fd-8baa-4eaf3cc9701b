# 第2章 主动信息收集

## 学习目标
- 理解主动信息收集的概念和特点
- 掌握网络扫描的基本原理和技术
- 学会使用各种扫描工具进行信息收集
- 了解服务识别和版本探测技术
- 理解主动信息收集的风险和对策

## 2.1 主动信息收集概述

### 2.1.1 什么是主动信息收集

主动信息收集（Active Information Gathering）是指通过直接与目标系统交互来获取信息的过程。与被动信息收集不同，主动信息收集会向目标系统发送数据包或请求，可能会在目标系统上留下访问痕迹。

### 2.1.2 主动信息收集的特点

#### 直接交互
- **系统接触**：直接与目标系统进行通信
- **实时信息**：获取实时的系统状态信息
- **准确性高**：信息准确性相对较高
- **深度探测**：可以进行深度的技术探测

#### 可检测性
- **日志记录**：会在目标系统产生访问日志
- **流量特征**：产生特定的网络流量特征
- **告警触发**：可能触发安全设备告警
- **痕迹明显**：留下明显的探测痕迹

#### 风险性
- **法律风险**：可能涉及未授权访问
- **技术风险**：可能影响目标系统稳定性
- **检测风险**：容易被安全设备检测
- **响应风险**：可能引发安全响应

### 2.1.3 主动与被动信息收集的比较

| 特征 | 主动信息收集 | 被动信息收集 |
|------|-------------|-------------|
| **交互方式** | 直接交互 | 间接获取 |
| **信息准确性** | 高 | 中等 |
| **信息实时性** | 实时 | 可能过时 |
| **检测风险** | 高 | 低 |
| **法律风险** | 较高 | 较低 |
| **技术要求** | 较高 | 中等 |
| **信息深度** | 深 | 浅 |

## 2.2 网络发现和主机存活检测

### 2.2.1 网络发现技术

#### ICMP扫描
- **Ping扫描**：使用ICMP Echo请求检测主机存活
- **优点**：简单快速，消耗资源少
- **缺点**：容易被防火墙阻止
- **命令示例**：
  ```bash
  ping -c 1 ***********
  nmap -sn ***********/24
  ```

#### ARP扫描
- **原理**：在同一网段使用ARP协议发现主机
- **优点**：难以被阻止，准确性高
- **缺点**：只能在同一网段使用
- **工具使用**：
  ```bash
  arp-scan -l
  nmap -PR ***********/24
  netdiscover -r ***********/24
  ```

#### TCP扫描
- **TCP SYN扫描**：发送SYN包检测端口开放
- **TCP ACK扫描**：发送ACK包检测防火墙规则
- **TCP Connect扫描**：完整的TCP连接
- **示例命令**：
  ```bash
  nmap -sS ***********
  nmap -sA ***********
  nmap -sT ***********
  ```

#### UDP扫描
- **原理**：发送UDP包检测UDP服务
- **特点**：速度慢，准确性相对较低
- **应用**：发现DNS、DHCP、SNMP等服务
- **命令示例**：
  ```bash
  nmap -sU ***********
  nmap -sU --top-ports 100 ***********
  ```

### 2.2.2 主机存活检测技术

#### 多协议检测
- **组合使用**：结合多种协议提高检测准确性
- **协议选择**：根据网络环境选择合适协议
- **绕过防护**：绕过防火墙和IDS检测
- **Nmap示例**：
  ```bash
  nmap -PE -PP -PM -PO ***********/24
  nmap -PS22,80,443 ***********/24
  nmap -PA80,443 ***********/24
  ```

#### 时间控制
- **扫描速度**：控制扫描速度避免检测
- **时间间隔**：设置合理的时间间隔
- **并发控制**：控制并发连接数
- **参数设置**：
  ```bash
  nmap -T1 ***********/24  # 慢速扫描
  nmap -T4 ***********/24  # 快速扫描
  nmap --min-rate 100 ***********/24
  ```

#### 源地址伪造
- **IP伪造**：伪造源IP地址
- **MAC伪造**：伪造MAC地址
- **分布式扫描**：使用多个源地址
- **实现方法**：
  ```bash
  nmap -S ***********00 ***********
  nmap --spoof-mac 0 ***********
  ```

### 2.2.3 网络拓扑发现

#### 路由跟踪
- **traceroute**：跟踪数据包路径
- **路径发现**：发现网络路径和中间设备
- **网络架构**：了解网络架构和拓扑
- **工具使用**：
  ```bash
  traceroute example.com
  tracepath example.com
  mtr example.com
  ```

#### TTL分析
- **TTL值分析**：通过TTL值推断网络跳数
- **操作系统识别**：不同OS的默认TTL值不同
- **网络距离**：估算网络距离
- **分析方法**：
  ```bash
  ping -c 1 -t 1 ***********
  nmap --traceroute ***********
  ```

#### SNMP扫描
- **设备发现**：通过SNMP发现网络设备
- **设备信息**：获取设备型号、版本等信息
- **网络拓扑**：构建网络拓扑图
- **工具使用**：
  ```bash
  snmpwalk -v2c -c public ***********
  onesixtyone -c community.txt ***********/24
  ```

## 2.3 端口扫描技术

### 2.3.1 TCP端口扫描

#### TCP SYN扫描
- **原理**：发送SYN包，根据响应判断端口状态
- **优点**：速度快，隐蔽性相对较好
- **缺点**：需要root权限，可能被检测
- **状态判断**：
  - SYN/ACK响应：端口开放
  - RST响应：端口关闭
  - 无响应：端口被过滤

#### TCP Connect扫描
- **原理**：完整的TCP三次握手
- **优点**：不需要特殊权限，准确性高
- **缺点**：速度慢，容易被检测和记录
- **应用场景**：权限受限环境

#### TCP FIN扫描
- **原理**：发送FIN包探测端口
- **优点**：可以绕过某些防火墙
- **缺点**：不是所有系统都遵循RFC标准
- **响应分析**：
  - RST响应：端口关闭
  - 无响应：端口开放或被过滤

#### TCP Xmas扫描
- **原理**：设置FIN、PSH、URG标志位
- **特点**：类似FIN扫描，用于绕过防护
- **检测能力**：某些IDS可能无法检测
- **命令示例**：
  ```bash
  nmap -sX ***********
  ```

#### TCP NULL扫描
- **原理**：不设置任何TCP标志位
- **用途**：绕过简单的包过滤器
- **限制**：Windows系统响应不标准
- **使用方法**：
  ```bash
  nmap -sN ***********
  ```

### 2.3.2 UDP端口扫描

#### UDP扫描原理
- **发送UDP包**：向目标端口发送UDP数据包
- **响应分析**：
  - UDP响应：端口开放
  - ICMP端口不可达：端口关闭
  - 无响应：端口开放或被过滤

#### UDP扫描挑战
- **速度慢**：UDP扫描速度相对较慢
- **准确性低**：难以准确判断端口状态
- **防火墙影响**：容易被防火墙阻止
- **优化方法**：
  ```bash
  nmap -sU --top-ports 100 ***********
  nmap -sU -T4 ***********
  ```

#### 常见UDP服务
- **DNS (53)**：域名解析服务
- **DHCP (67/68)**：动态主机配置
- **SNMP (161)**：简单网络管理协议
- **NTP (123)**：网络时间协议
- **TFTP (69)**：简单文件传输协议

### 2.3.3 端口扫描优化

#### 扫描策略
- **端口选择**：优先扫描常见端口
- **分阶段扫描**：先快速扫描，再详细扫描
- **目标分组**：将目标分组并行扫描
- **结果过滤**：过滤无用的扫描结果

#### 性能优化
- **并发控制**：
  ```bash
  nmap --min-parallelism 100 ***********/24
  nmap --max-parallelism 300 ***********/24
  ```
- **时间控制**：
  ```bash
  nmap --min-rtt-timeout 100ms ***********
  nmap --max-rtt-timeout 500ms ***********
  ```
- **重试控制**：
  ```bash
  nmap --max-retries 2 ***********
  ```

#### 规避检测
- **分片扫描**：
  ```bash
  nmap -f ***********
  nmap --mtu 16 ***********
  ```
- **诱饵扫描**：
  ```bash
  nmap -D RND:10 ***********
  nmap -D ***********00,***********01,ME ***********
  ```
- **源端口伪造**：
  ```bash
  nmap --source-port 53 ***********
  nmap -g 80 ***********
  ```

## 2.4 服务识别和版本探测

### 2.4.1 服务识别技术

#### Banner抓取
- **原理**：连接服务获取Banner信息
- **信息内容**：服务名称、版本、操作系统
- **实现方法**：
  ```bash
  nc *********** 80
  telnet *********** 25
  nmap -sV ***********
  ```

#### 协议探测
- **HTTP探测**：
  ```bash
  curl -I http://***********
  wget --spider --server-response http://***********
  ```
- **SSH探测**：
  ```bash
  ssh -V ***********
  ```
- **FTP探测**：
  ```bash
  ftp ***********
  ```

#### 指纹识别
- **服务指纹**：基于服务响应特征识别
- **协议指纹**：基于协议实现差异识别
- **行为指纹**：基于服务行为模式识别
- **工具使用**：
  ```bash
  nmap -sV --version-intensity 9 ***********
  amap *********** 80
  ```

### 2.4.2 操作系统识别

#### TCP/IP栈指纹
- **TTL值**：不同OS的默认TTL值
- **窗口大小**：TCP窗口大小特征
- **选项字段**：TCP选项字段差异
- **分片处理**：IP分片处理方式

#### 主动OS探测
- **Nmap OS检测**：
  ```bash
  nmap -O ***********
  nmap -O --osscan-guess ***********
  ```
- **p0f被动检测**：
  ```bash
  p0f -i eth0
  ```

#### 应用层特征
- **HTTP头信息**：Server字段信息
- **错误页面**：默认错误页面特征
- **默认文件**：默认安装文件和目录
- **服务响应**：特定服务的响应特征

### 2.4.3 漏洞扫描

#### 漏洞扫描原理
- **已知漏洞**：检测已知的安全漏洞
- **配置错误**：检测安全配置错误
- **弱密码**：检测默认或弱密码
- **版本漏洞**：基于版本信息匹配漏洞

#### 扫描工具
- **Nmap脚本**：
  ```bash
  nmap --script vuln ***********
  nmap --script "not intrusive" ***********
  ```
- **OpenVAS**：开源漏洞扫描器
- **Nessus**：商业漏洞扫描器
- **Nikto**：Web漏洞扫描器

#### 扫描策略
- **分类扫描**：按漏洞类型分类扫描
- **风险评估**：评估漏洞风险等级
- **误报处理**：识别和处理误报
- **结果验证**：手工验证扫描结果

## 2.5 Web应用探测

### 2.5.1 Web服务器识别

#### HTTP头分析
- **Server字段**：Web服务器类型和版本
- **X-Powered-By**：后端技术栈信息
- **Set-Cookie**：会话管理机制
- **其他头部**：自定义头部信息

#### 错误页面分析
- **404页面**：默认404错误页面
- **500页面**：服务器内部错误页面
- **403页面**：访问禁止页面
- **特征识别**：基于错误页面识别技术栈

#### 默认文件检测
- **robots.txt**：爬虫规则文件
- **sitemap.xml**：网站地图文件
- **favicon.ico**：网站图标文件
- **crossdomain.xml**：跨域策略文件

### 2.5.2 目录和文件发现

#### 字典攻击
- **常见目录**：admin、backup、test等
- **常见文件**：config.php、database.sql等
- **工具使用**：
  ```bash
  dirb http://***********
  gobuster dir -u http://*********** -w wordlist.txt
  dirsearch -u http://***********
  ```

#### 爬虫技术
- **链接提取**：从页面中提取链接
- **递归爬取**：递归爬取发现的链接
- **表单发现**：发现表单和参数
- **工具推荐**：
  ```bash
  wget --spider --recursive http://***********
  httrack http://***********
  ```

#### 备份文件发现
- **备份扩展名**：.bak、.old、.backup、.orig
- **压缩文件**：.zip、.tar.gz、.rar
- **临时文件**：.tmp、.temp、~
- **版本控制**：.git、.svn、.hg

### 2.5.3 Web应用技术栈

#### 前端技术
- **JavaScript框架**：React、Vue、Angular
- **CSS框架**：Bootstrap、Foundation
- **UI库**：jQuery、jQuery UI
- **模板引擎**：Handlebars、Mustache

#### 后端技术
- **编程语言**：PHP、Python、Java、.NET
- **Web框架**：Laravel、Django、Spring
- **模板引擎**：Twig、Jinja2、Thymeleaf
- **ORM框架**：Eloquent、SQLAlchemy、Hibernate

#### 数据库技术
- **关系型数据库**：MySQL、PostgreSQL、SQL Server
- **NoSQL数据库**：MongoDB、Redis、Elasticsearch
- **数据库连接**：连接池、ORM配置
- **缓存系统**：Redis、Memcached

## 2.6 主动扫描的风险和对策

### 2.6.1 扫描风险

#### 法律风险
- **未授权访问**：可能构成非法入侵
- **服务中断**：扫描可能导致服务中断
- **数据泄露**：可能触发数据保护法规
- **合规问题**：违反行业合规要求

#### 技术风险
- **系统崩溃**：过度扫描可能导致系统崩溃
- **性能影响**：大量请求影响系统性能
- **数据损坏**：某些扫描可能损坏数据
- **服务拒绝**：触发DoS保护机制

#### 检测风险
- **IDS告警**：触发入侵检测系统告警
- **日志记录**：在目标系统留下访问日志
- **流量分析**：异常流量被安全设备发现
- **行为分析**：异常行为被安全分析发现

### 2.6.2 规避检测技术

#### 扫描速度控制
- **慢速扫描**：降低扫描速度
- **随机间隔**：使用随机时间间隔
- **分布式扫描**：使用多个源地址
- **时间分散**：将扫描分散到不同时间

#### 流量伪装
- **正常流量模拟**：模拟正常用户行为
- **User-Agent伪造**：伪造浏览器标识
- **Referer伪造**：伪造来源页面
- **协议伪装**：使用合法协议承载扫描

#### 源地址隐藏
- **代理链**：使用代理链隐藏真实IP
- **Tor网络**：通过Tor网络匿名扫描
- **VPN服务**：使用VPN隐藏源地址
- **僵尸网络**：使用分布式僵尸网络

### 2.6.3 防护对策

#### 网络层防护
- **防火墙规则**：配置防火墙阻止扫描
- **速率限制**：限制连接速率
- **IP黑名单**：将扫描源IP加入黑名单
- **地理位置过滤**：基于地理位置过滤

#### 应用层防护
- **WAF部署**：部署Web应用防火墙
- **访问控制**：实施严格的访问控制
- **输入验证**：验证所有输入数据
- **错误处理**：安全的错误处理机制

#### 监控和检测
- **异常检测**：检测异常访问模式
- **行为分析**：分析用户行为模式
- **威胁情报**：集成威胁情报信息
- **自动响应**：自动化安全响应机制

## 本章小结

主动信息收集通过直接与目标系统交互获取详细的技术信息，包括网络拓扑、主机存活、端口状态、服务版本等。虽然这种方法信息准确性高，但也存在较高的检测风险和法律风险。在进行主动信息收集时，需要合理控制扫描强度，采用规避检测技术，并严格遵守法律法规要求。

## 实验练习

1. 使用Nmap进行网络发现和端口扫描
2. 进行服务识别和操作系统指纹识别
3. 使用Web扫描工具发现Web应用信息
4. 实践扫描规避技术和隐蔽扫描
5. 分析扫描结果并生成目标档案

## 思考题

1. 主动信息收集相比被动信息收集有什么优缺点？
2. 如何在保证扫描效果的同时降低被检测的风险？
3. 不同类型的端口扫描技术适用于什么场景？
4. 如何设计有效的扫描防护策略？
5. 在合法授权的渗透测试中如何合理使用主动扫描？
