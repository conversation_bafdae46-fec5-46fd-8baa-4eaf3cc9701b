# 第1章 被动信息收集

## 学习目标
- 理解被动信息收集的概念和重要性
- 掌握各种被动信息收集的技术和方法
- 了解开源情报（OSINT）的应用
- 学会使用各种被动信息收集工具
- 理解被动信息收集的法律和道德边界

## 1.1 被动信息收集概述

### 1.1.1 什么是被动信息收集

被动信息收集（Passive Information Gathering）是指在不直接与目标系统交互的情况下，通过公开可获得的信息源来收集目标相关信息的过程。这种方法不会在目标系统上留下明显的痕迹，因此被称为"被动"收集。

### 1.1.2 被动信息收集的特点

#### 隐蔽性
- **无直接接触**：不直接访问目标系统
- **无日志记录**：不会在目标系统产生访问日志
- **难以检测**：目标很难发现信息收集活动
- **低风险**：法律和技术风险相对较低

#### 公开性
- **公开信息源**：使用公开可获得的信息源
- **合法获取**：通过合法渠道获取信息
- **无需授权**：通常不需要特殊授权
- **广泛可用**：信息源广泛且容易获取

#### 有效性
- **信息丰富**：可以获得大量有价值的信息
- **成本低廉**：成本相对较低
- **效率较高**：可以快速获得初步信息
- **基础重要**：为后续测试提供重要基础

### 1.1.3 被动信息收集的价值

#### 攻击面识别
- **资产发现**：发现目标拥有的数字资产
- **技术栈识别**：了解目标使用的技术栈
- **服务识别**：识别对外提供的服务
- **入口点发现**：找到可能的攻击入口点

#### 社会工程准备
- **人员信息**：收集关键人员信息
- **组织结构**：了解组织架构和关系
- **业务信息**：了解业务模式和流程
- **文化背景**：了解组织文化和习惯

#### 风险评估
- **暴露面分析**：分析信息暴露情况
- **安全意识评估**：评估安全意识水平
- **合规性检查**：检查信息披露合规性
- **威胁建模**：为威胁建模提供输入

## 1.2 搜索引擎技术

### 1.2.1 Google搜索技巧

#### 基本搜索语法
- **精确匹配**：使用引号进行精确搜索
  ```
  "example.com"
  ```
- **排除关键词**：使用减号排除特定词汇
  ```
  site:example.com -www
  ```
- **通配符搜索**：使用星号作为通配符
  ```
  "admin * password"
  ```

#### 高级搜索操作符
- **site:**：限制搜索特定网站
  ```
  site:example.com filetype:pdf
  ```
- **filetype:**：搜索特定文件类型
  ```
  filetype:doc site:example.com
  ```
- **inurl:**：搜索URL中包含特定词汇的页面
  ```
  inurl:admin site:example.com
  ```
- **intitle:**：搜索标题中包含特定词汇的页面
  ```
  intitle:"index of" site:example.com
  ```
- **intext:**：搜索正文中包含特定词汇的页面
  ```
  intext:"password" site:example.com
  ```

#### Google Dorking
- **敏感文件搜索**：
  ```
  site:example.com filetype:sql "INSERT INTO"
  site:example.com filetype:log
  site:example.com filetype:bak
  ```
- **配置文件搜索**：
  ```
  site:example.com "config" filetype:conf
  site:example.com "database" filetype:yml
  ```
- **错误页面搜索**：
  ```
  site:example.com "error" "sql"
  site:example.com "warning" "mysql"
  ```

### 1.2.2 其他搜索引擎

#### Bing搜索
- **独特功能**：IP地址搜索、反向IP查找
- **搜索语法**：类似Google但有细微差别
- **特殊用途**：某些内容在Bing中更容易找到

#### DuckDuckGo
- **隐私保护**：不跟踪用户搜索历史
- **无过滤**：不基于用户画像过滤结果
- **特殊功能**：!bang命令直接搜索特定网站

#### Shodan
- **物联网搜索**：专门搜索联网设备
- **服务识别**：识别设备运行的服务
- **地理定位**：按地理位置搜索设备
- **搜索示例**：
  ```
  org:"Example Corp"
  country:"CN" port:22
  "default password" country:"US"
  ```

#### Censys
- **证书搜索**：搜索SSL/TLS证书
- **主机发现**：发现主机和服务
- **IPv4扫描**：基于IPv4扫描数据
- **搜索示例**：
  ```
  parsed.names: example.com
  protocols: "443/https"
  location.country: "United States"
  ```

### 1.2.3 专业搜索工具

#### theHarvester
- **功能**：自动化信息收集工具
- **数据源**：Google、Bing、LinkedIn等
- **输出信息**：邮箱、子域名、主机名
- **使用示例**：
  ```bash
  theHarvester -d example.com -l 100 -b google
  theHarvester -d example.com -b all
  ```

#### Maltego
- **图形化工具**：可视化信息关联分析
- **数据变换**：自动化数据收集和分析
- **关系映射**：展示实体之间的关系
- **应用场景**：复杂的信息关联分析

#### SpiderFoot
- **自动化平台**：全自动化OSINT平台
- **多数据源**：集成100+数据源
- **Web界面**：友好的Web管理界面
- **报告生成**：自动生成详细报告

## 1.3 域名和DNS信息收集

### 1.3.1 域名注册信息

#### WHOIS查询
- **注册信息**：域名注册者、注册商、注册时间
- **联系信息**：管理员、技术联系人邮箱和电话
- **DNS服务器**：权威DNS服务器信息
- **查询工具**：
  ```bash
  whois example.com
  whois -h whois.arin.net 192.168.1.1
  ```

#### 域名历史
- **历史WHOIS**：查询域名历史注册信息
- **所有权变更**：跟踪域名所有权变更
- **工具推荐**：DomainTools、WhoisXML API
- **应用价值**：了解组织历史和关联关系

#### 域名过期信息
- **过期时间**：域名到期时间
- **续费历史**：域名续费历史记录
- **风险评估**：评估域名劫持风险
- **监控设置**：设置域名过期监控

### 1.3.2 子域名发现

#### 搜索引擎方法
- **Google搜索**：
  ```
  site:*.example.com
  site:example.com -www
  ```
- **证书透明度日志**：
  ```
  crt.sh: %.example.com
  ```

#### DNS枚举
- **字典攻击**：使用常见子域名字典
- **DNS暴力破解**：
  ```bash
  dnsrecon -d example.com -t brt -D subdomains.txt
  sublist3r -d example.com
  ```
- **DNS区域传输**：
  ```bash
  dig axfr @ns1.example.com example.com
  ```

#### 专业工具
- **Sublist3r**：多源子域名收集
- **Amass**：深度子域名发现
- **Subfinder**：快速子域名发现
- **使用示例**：
  ```bash
  amass enum -d example.com
  subfinder -d example.com -silent
  ```

### 1.3.3 DNS记录分析

#### 常见DNS记录类型
- **A记录**：域名到IPv4地址映射
- **AAAA记录**：域名到IPv6地址映射
- **CNAME记录**：域名别名记录
- **MX记录**：邮件交换记录
- **TXT记录**：文本记录，包含SPF、DKIM等
- **NS记录**：权威名称服务器记录

#### DNS查询工具
- **dig命令**：
  ```bash
  dig example.com A
  dig example.com MX
  dig example.com TXT
  dig @******* example.com ANY
  ```
- **nslookup命令**：
  ```bash
  nslookup example.com
  nslookup -type=MX example.com
  ```

#### DNS信息分析
- **基础设施识别**：识别使用的DNS服务商
- **邮件系统识别**：通过MX记录识别邮件系统
- **CDN识别**：通过CNAME记录识别CDN服务
- **安全配置检查**：检查SPF、DMARC、DKIM配置

## 1.4 社交媒体和人员信息

### 1.4.1 社交媒体平台

#### LinkedIn信息收集
- **员工信息**：收集员工姓名、职位、技能
- **组织架构**：了解组织结构和部门设置
- **技术栈**：通过员工技能了解技术栈
- **联系信息**：获取员工联系方式

#### Facebook/Twitter信息
- **个人信息**：收集个人兴趣、家庭信息
- **地理位置**：通过签到信息获取位置
- **社交关系**：了解人际关系网络
- **时间模式**：分析活动时间模式

#### 专业平台
- **GitHub**：代码仓库、技术能力、项目信息
- **Stack Overflow**：技术问题、使用的技术
- **技术博客**：技术文章、专业能力
- **会议演讲**：参与的技术会议和演讲

### 1.4.2 人员信息收集技术

#### 邮箱地址收集
- **格式推测**：根据已知邮箱推测格式
- **搜索引擎**：使用搜索引擎查找邮箱
- **社交媒体**：从社交媒体收集邮箱
- **工具使用**：
  ```bash
  theHarvester -d example.com -b all
  hunter.io API查询
  ```

#### 用户名枚举
- **命名规律**：分析用户名命名规律
- **跨平台搜索**：在多个平台搜索相同用户名
- **工具推荐**：Sherlock、WhatsMyName
- **使用示例**：
  ```bash
  sherlock username
  ```

#### 密码策略分析
- **密码模式**：分析组织密码策略
- **常用密码**：收集可能的常用密码
- **个人信息**：基于个人信息构造密码字典
- **季节性密码**：考虑季节性密码变化

### 1.4.3 组织信息收集

#### 公司基本信息
- **工商信息**：注册信息、股东信息、经营范围
- **财务信息**：年报、财务状况、投资关系
- **新闻报道**：媒体报道、行业动态
- **官方网站**：公司介绍、产品服务、联系方式

#### 业务信息
- **产品服务**：主要产品和服务
- **客户信息**：主要客户和合作伙伴
- **供应链**：供应商和合作关系
- **市场定位**：行业地位和竞争对手

#### 技术信息
- **技术栈**：使用的技术和平台
- **供应商**：IT供应商和服务商
- **招聘信息**：技术岗位招聘需求
- **专利信息**：技术专利和知识产权

## 1.5 网站和应用信息

### 1.5.1 网站技术栈识别

#### 前端技术识别
- **JavaScript框架**：React、Vue、Angular等
- **CSS框架**：Bootstrap、Foundation等
- **字体和图标**：Font Awesome、Google Fonts等
- **CDN服务**：CloudFlare、AWS CloudFront等

#### 后端技术识别
- **Web服务器**：Apache、Nginx、IIS等
- **编程语言**：PHP、Python、Java、.NET等
- **数据库**：MySQL、PostgreSQL、MongoDB等
- **框架识别**：Laravel、Django、Spring等

#### 识别工具
- **Wappalyzer**：浏览器插件，自动识别技术栈
- **BuiltWith**：在线技术栈分析工具
- **WhatWeb**：命令行技术识别工具
- **使用示例**：
  ```bash
  whatweb example.com
  ```

### 1.5.2 网站结构分析

#### 目录结构发现
- **robots.txt**：查看robots.txt文件
- **sitemap.xml**：查看网站地图
- **目录枚举**：使用字典枚举目录
- **工具使用**：
  ```bash
  dirb http://example.com
  gobuster dir -u http://example.com -w wordlist.txt
  ```

#### 文件类型分析
- **静态文件**：图片、CSS、JavaScript文件
- **动态文件**：PHP、ASP、JSP等动态页面
- **配置文件**：可能泄露的配置文件
- **备份文件**：.bak、.old、.backup等备份文件

#### 隐藏内容发现
- **注释信息**：HTML注释中的敏感信息
- **源码泄露**：.git、.svn等版本控制目录
- **测试页面**：test、dev、staging等测试页面
- **管理界面**：admin、manager等管理页面

### 1.5.3 第三方服务识别

#### CDN和云服务
- **CDN识别**：CloudFlare、Akamai、AWS CloudFront
- **云平台**：AWS、Azure、Google Cloud
- **托管服务**：GitHub Pages、Netlify、Vercel
- **识别方法**：DNS记录、HTTP头、IP地址范围

#### 分析工具和服务
- **Google Analytics**：网站分析工具
- **广告服务**：Google AdSense、Facebook Pixel
- **客服系统**：在线客服、聊天工具
- **支付系统**：支付网关、电商平台

#### 安全服务
- **WAF识别**：Web应用防火墙
- **DDoS防护**：DDoS防护服务
- **SSL证书**：证书颁发机构和类型
- **安全扫描**：安全扫描服务标识

## 1.6 被动信息收集的自动化

### 1.6.1 自动化工具

#### 综合性工具
- **Recon-ng**：模块化侦察框架
- **OSINT Framework**：OSINT工具集合
- **Maltego**：图形化关联分析
- **SpiderFoot**：自动化OSINT平台

#### 专业化工具
- **域名相关**：Sublist3r、Amass、Subfinder
- **邮箱收集**：theHarvester、Hunter.io
- **社交媒体**：Sherlock、Social Mapper
- **技术识别**：WhatWeb、Wappalyzer

#### 脚本和API
- **自定义脚本**：Python、Bash脚本自动化
- **API集成**：集成各种在线服务API
- **数据处理**：自动化数据清洗和分析
- **报告生成**：自动生成信息收集报告

### 1.6.2 信息整合和分析

#### 数据去重和清洗
- **重复数据**：去除重复的信息
- **数据验证**：验证信息的准确性
- **格式统一**：统一数据格式
- **质量评估**：评估信息质量

#### 关联分析
- **实体关联**：分析不同实体之间的关系
- **时间关联**：分析时间序列关系
- **地理关联**：分析地理位置关系
- **技术关联**：分析技术栈关联关系

#### 威胁建模
- **攻击面分析**：基于收集信息分析攻击面
- **风险评估**：评估潜在安全风险
- **优先级排序**：对发现的问题进行优先级排序
- **行动计划**：制定后续行动计划

## 1.7 法律和道德考虑

### 1.7.1 法律边界

#### 公开信息原则
- **公开可获得**：只使用公开可获得的信息
- **合法渠道**：通过合法渠道获取信息
- **无需授权**：不需要特殊授权的信息
- **遵守条款**：遵守网站使用条款

#### 隐私保护
- **个人隐私**：尊重个人隐私权
- **数据保护**：遵守数据保护法规
- **敏感信息**：谨慎处理敏感信息
- **信息使用**：合理使用收集的信息

### 1.7.2 道德准则

#### 最小化原则
- **必要性**：只收集必要的信息
- **相关性**：确保信息与目标相关
- **比例性**：收集程度与目的相匹配
- **时效性**：及时删除不再需要的信息

#### 负责任披露
- **漏洞发现**：发现安全问题时负责任披露
- **影响评估**：评估披露的潜在影响
- **协调披露**：与相关方协调披露时间
- **公共利益**：考虑公共利益

### 1.7.3 最佳实践

#### 文档记录
- **收集过程**：记录信息收集过程
- **信息来源**：记录信息的来源
- **时间戳**：记录收集时间
- **方法说明**：说明使用的方法和工具

#### 信息保护
- **访问控制**：限制信息访问权限
- **加密存储**：加密存储敏感信息
- **安全传输**：安全传输信息
- **定期清理**：定期清理不需要的信息

## 本章小结

被动信息收集是渗透测试的重要第一步，通过搜索引擎、DNS查询、社交媒体等公开渠道收集目标信息。这种方法隐蔽性强、风险较低，能够为后续测试提供重要基础。在进行被动信息收集时，必须遵守法律法规和道德准则，确保信息收集活动的合法性和合理性。

## 实验练习

1. 使用Google Dorking技术收集目标网站信息
2. 使用theHarvester收集邮箱地址和子域名
3. 分析目标网站的技术栈和第三方服务
4. 通过社交媒体收集人员信息
5. 使用自动化工具进行综合信息收集

## 思考题

1. 被动信息收集与主动信息收集有什么区别？
2. 如何确保被动信息收集的合法性？
3. 哪些信息源最有价值？为什么？
4. 如何提高信息收集的效率和准确性？
5. 如何防范他人对自己组织的信息收集？
